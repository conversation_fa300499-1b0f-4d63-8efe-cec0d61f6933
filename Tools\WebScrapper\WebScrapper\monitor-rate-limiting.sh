#!/bin/bash

# Rate Limiting Monitor Script
# This script helps monitor the effectiveness of rate limiting

echo "Apache Rate Limiting Monitor"
echo "============================"
echo ""

# Function to show top IPs by request count
show_top_ips() {
    echo "Top 10 IPs by request count (last 1000 lines):"
    echo "-----------------------------------------------"
    sudo tail -1000 /var/log/apache2/access.log | awk '{print $1}' | sort | uniq -c | sort -nr | head -10
    echo ""
}

# Function to show rate limit violations
show_rate_limit_violations() {
    echo "Recent rate limit violations (HTTP 429):"
    echo "----------------------------------------"
    sudo grep " 429 " /var/log/apache2/access.log | tail -10
    echo ""
}

# Function to show ModSecurity blocks
show_modsec_blocks() {
    echo "Recent ModSecurity blocks:"
    echo "-------------------------"
    if [ -f "/var/log/apache2/modsec_audit.log" ]; then
        sudo grep -A 5 -B 5 "rate limit" /var/log/apache2/modsec_audit.log | tail -20
    else
        echo "ModSecurity audit log not found"
    fi
    echo ""
}

# Function to show evasive blocks
show_evasive_blocks() {
    echo "Evasive module blocks:"
    echo "---------------------"
    if [ -d "/var/log/apache2/evasive" ]; then
        sudo find /var/log/apache2/evasive -name "dos-*" -mtime -1 -exec basename {} \; | head -10
        if [ $? -ne 0 ]; then
            echo "No recent evasive blocks found"
        fi
    else
        echo "Evasive log directory not found"
    fi
    echo ""
}

# Function to show current connections
show_current_connections() {
    echo "Current Apache connections:"
    echo "--------------------------"
    sudo netstat -an | grep :443 | grep ESTABLISHED | wc -l
    echo "Active HTTPS connections"
    sudo netstat -an | grep :80 | grep ESTABLISHED | wc -l
    echo "Active HTTP connections"
    echo ""
}

# Function to show real-time monitoring
real_time_monitor() {
    echo "Real-time access log monitoring (Ctrl+C to stop):"
    echo "------------------------------------------------"
    sudo tail -f /var/log/apache2/access.log | while read line; do
        ip=$(echo $line | awk '{print $1}')
        status=$(echo $line | awk '{print $9}')
        request=$(echo $line | awk '{print $7}')
        
        if [ "$status" = "429" ]; then
            echo "[RATE LIMITED] $ip - $request"
        elif [ "$status" = "403" ]; then
            echo "[BLOCKED] $ip - $request"
        elif [ "$status" = "200" ]; then
            echo "[OK] $ip - $request"
        else
            echo "[$status] $ip - $request"
        fi
    done
}

# Function to analyze request patterns
analyze_patterns() {
    echo "Request pattern analysis (last 1000 requests):"
    echo "----------------------------------------------"
    
    echo "Requests by status code:"
    sudo tail -1000 /var/log/apache2/access.log | awk '{print $9}' | sort | uniq -c | sort -nr
    echo ""
    
    echo "Most requested URLs:"
    sudo tail -1000 /var/log/apache2/access.log | awk '{print $7}' | sort | uniq -c | sort -nr | head -10
    echo ""
    
    echo "User agents causing most requests:"
    sudo tail -1000 /var/log/apache2/access.log | awk -F'"' '{print $6}' | sort | uniq -c | sort -nr | head -5
    echo ""
}

# Main menu
case "$1" in
    "ips")
        show_top_ips
        ;;
    "violations")
        show_rate_limit_violations
        ;;
    "modsec")
        show_modsec_blocks
        ;;
    "evasive")
        show_evasive_blocks
        ;;
    "connections")
        show_current_connections
        ;;
    "realtime")
        real_time_monitor
        ;;
    "patterns")
        analyze_patterns
        ;;
    "all")
        show_top_ips
        show_rate_limit_violations
        show_modsec_blocks
        show_evasive_blocks
        show_current_connections
        analyze_patterns
        ;;
    *)
        echo "Usage: $0 {ips|violations|modsec|evasive|connections|realtime|patterns|all}"
        echo ""
        echo "Commands:"
        echo "  ips         - Show top IPs by request count"
        echo "  violations  - Show recent rate limit violations (HTTP 429)"
        echo "  modsec      - Show ModSecurity blocks"
        echo "  evasive     - Show evasive module blocks"
        echo "  connections - Show current connection count"
        echo "  realtime    - Real-time log monitoring"
        echo "  patterns    - Analyze request patterns"
        echo "  all         - Show all information"
        echo ""
        exit 1
        ;;
esac
