using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WebScrapper.Models;
using WebScrapper.Services;
using WebScrapper.Validators;

namespace WebScrapper.Controllers;
[Authorize]
[ApiController]
[Route("api/[controller]")]
public class PersonDetailsController: ControllerBase
{
    private readonly IWebScrapperService _webScrapperService;
    private readonly ILogger<PersonDetailsController> _logger;
    private readonly ChRegisterUrlValidator _urlValidator;

    public PersonDetailsController(
        IWebScrapperService webScrapperService, 
        ILogger<PersonDetailsController> logger,
        ChRegisterUrlValidator urlValidator)
    {
        _webScrapperService = webScrapperService;
        _logger = logger;
        _urlValidator = urlValidator;
    }

    [HttpGet(Name = "GetPersonDetails")]
    public async Task<ActionResult<IEnumerable<PersonInfo>>> GetPeople(string url)
    {
        var (isValid, cantonCode, cheNumber) = _urlValidator.Validate(url);

        if (!isValid)
        {
            _logger.LogWarning("Invalid URL format: {Url}", url);
            return BadRequest($"Invalid URL format. Expected format: {ChRegisterUrlValidator.GetExpectedFormat()}");
        }

        _logger.LogInformation("Processing request for canton: {CantonCode}, CHE: {CheNumber}", cantonCode, cheNumber);

        var people = await _webScrapperService.GetPersonDetailsAsync(url);

        if(people == null || people.Count == 0)
        {
            _logger.LogError("No people found for canton: {CantonCode}, CHE: {CheNumber}", cantonCode, cheNumber);
            return NotFound("No people found");
        }
        return Ok(people);
    }
}
