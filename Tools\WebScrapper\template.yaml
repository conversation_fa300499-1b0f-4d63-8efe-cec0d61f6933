# yaml-language-server: $schema=https://raw.githubusercontent.com/aws/serverless-application-model/main/samtranslator/schema/schema.json
AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  WebScrapper Lambda Function
  
  Web scraping service for extracting person details from Swiss company register

# More info about Globals: https://github.com/awslabs/serverless-application-model/blob/master/docs/globals.rst
Globals:
  Function:
    Timeout: 300
    MemorySize: 1024
    Runtime: dotnet8
    Architectures:
      - x86_64
    Environment:
      Variables:
        ASPNETCORE_ENVIRONMENT: Production

Parameters:
  BasicAuthUsername:
    Type: String
    Default: alanadmin
    Description: Username for basic authentication
  BasicAuthPassword:
    Type: String
    NoEcho: true
    Default: alan!@21ALAN
    Description: Password for basic authentication

Resources:
  WebScrapperFunction:
    Type: AWS::Serverless::Function # More info about Function Resource: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction
    Properties:
      CodeUri: WebScrapper/
      Handler: WebScrapper::WebScrapper.Function::FunctionHandler
      Runtime: dotnet8
      Architectures:
        - x86_64
      MemorySize: 2048
      Timeout: 300
      Environment:
        Variables:
          BASIC_AUTH_USERNAME:
            Ref: BasicAuthUsername
          BASIC_AUTH_PASSWORD:
            Ref: BasicAuthPassword
      Layers:
        # Using public Chrome layer - replace with your region's layer ARN
        # US East 1: arn:aws:lambda:us-east-1:764866452798:layer:chrome-aws-lambda:31
        # US West 2: arn:aws:lambda:us-west-2:764866452798:layer:chrome-aws-lambda:31
        # EU West 1: arn:aws:lambda:eu-west-1:764866452798:layer:chrome-aws-lambda:31
        - arn:aws:lambda:us-east-1:764866452798:layer:chrome-aws-lambda:31
      Events:
        PersonDetails:
          Type: Api # More info about API Event Source: https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#api
          Properties:
            Path: /PersonDetails
            Method: get
            RestApiId:
              Ref: WebScrapperApi
        Test:
          Type: Api
          Properties:
            Path: /Test
            Method: get
            RestApiId:
              Ref: WebScrapperApi
        Root:
          Type: Api
          Properties:
            Path: /
            Method: get
            RestApiId:
              Ref: WebScrapperApi

  # Explicit API Gateway with CORS configuration
  WebScrapperApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: Prod
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,Authorization'"
        AllowOrigin: "'*'"

Outputs:
  # ServerlessRestApi is an implicit API created out of Events key under Serverless::Function
  # Find out more about other implicit resources you can reference within SAM
  # https://github.com/awslabs/serverless-application-model/blob/master/docs/internals/generated_resources.rst#api
  WebScrapperApiUrl:
    Description: "API Gateway endpoint URL for Prod stage for WebScrapper function"
    Value:
      Sub: "https://${WebScrapperApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/"
  WebScrapperFunctionArn:
    Description: "WebScrapper Lambda Function ARN"
    Value:
      Fn::GetAtt:
        - WebScrapperFunction
        - Arn
  WebScrapperFunctionName:
    Description: "WebScrapper Lambda Function Name"
    Value:
      Ref: WebScrapperFunction
