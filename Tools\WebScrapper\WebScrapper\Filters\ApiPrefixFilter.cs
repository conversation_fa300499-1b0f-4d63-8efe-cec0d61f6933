using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace WebScrapper.Filters;

public class ApiPrefixFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // Update the Swagger document to reflect the /api prefix
        var paths = new OpenApiPaths();
        foreach (var path in swaggerDoc.Paths)
        {
            paths.Add(path.Key, path.Value);
        }
        swaggerDoc.Paths = paths;
    }
}