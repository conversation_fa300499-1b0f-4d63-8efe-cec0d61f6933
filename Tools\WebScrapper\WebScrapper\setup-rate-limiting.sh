#!/bin/bash

# Apache Rate Limiting Setup Script
# This script enables the required modules and sets up rate limiting

echo "Setting up Apache Rate Limiting..."

# Update package list
sudo apt-get update

# Install required packages
echo "Installing required packages..."
sudo apt-get install -y libapache2-mod-evasive libapache2-mod-security2 libapache2-mod-limitipconn

# Enable required Apache modules
echo "Enabling Apache modules..."
sudo a2enmod evasive
sudo a2enmod security2
sudo a2enmod limitipconn
sudo a2enmod headers
sudo a2enmod rewrite
sudo a2enmod proxy
sudo a2enmod proxy_http

# Create necessary directories
echo "Creating directories..."
sudo mkdir -p /var/log/apache2/evasive
sudo mkdir -p /etc/apache2/modsecurity
sudo mkdir -p /tmp/modsecurity

# Set proper permissions
sudo chown www-data:www-data /var/log/apache2/evasive
sudo chown www-data:www-data /tmp/modsecurity
sudo chmod 755 /var/log/apache2/evasive
sudo chmod 755 /tmp/modsecurity

# Copy bad user agents file to modsecurity directory
if [ -f "bad-user-agents.txt" ]; then
    sudo cp bad-user-agents.txt /etc/apache2/modsecurity/
    echo "Copied bad-user-agents.txt to /etc/apache2/modsecurity/"
fi

# Create basic ModSecurity configuration if it doesn't exist
if [ ! -f "/etc/apache2/mods-enabled/security2.conf" ]; then
    echo "Creating basic ModSecurity configuration..."
    sudo tee /etc/apache2/mods-enabled/security2.conf > /dev/null <<EOF
<IfModule mod_security2.c>
    # Basic ModSecurity configuration
    SecRuleEngine On
    SecRequestBodyAccess On
    SecResponseBodyAccess Off
    SecRequestBodyLimit 13107200
    SecRequestBodyNoFilesLimit 131072
    SecRequestBodyInMemoryLimit 131072
    SecRequestBodyLimitAction Reject
    SecTmpDir /tmp/modsecurity/
    SecDataDir /tmp/modsecurity/
    SecAuditEngine RelevantOnly
    SecAuditLogRelevantStatus "^(?:5|4(?!04))"
    SecAuditLogParts ABDEFHIJZ
    SecAuditLogType Serial
    SecAuditLog /var/log/apache2/modsec_audit.log
</IfModule>
EOF
fi

# Test Apache configuration
echo "Testing Apache configuration..."
sudo apache2ctl configtest

if [ $? -eq 0 ]; then
    echo "Configuration test passed!"
    echo "Restarting Apache..."
    sudo systemctl restart apache2
    echo "Apache restarted successfully!"
    
    echo ""
    echo "Rate limiting setup complete!"
    echo ""
    echo "Configuration details:"
    echo "- mod_evasive: DDoS protection (2 requests/page/second, 50 requests/site/second)"
    echo "- mod_security: Application layer rate limiting (100 requests/minute/IP)"
    echo "- mod_limitipconn: Connection limiting (10 concurrent connections/IP)"
    echo ""
    echo "Log files:"
    echo "- Evasive logs: /var/log/apache2/evasive/"
    echo "- ModSecurity audit log: /var/log/apache2/modsec_audit.log"
    echo "- Access log with timing: /var/log/apache2/access.log"
    echo ""
    echo "To monitor rate limiting:"
    echo "  sudo tail -f /var/log/apache2/access.log"
    echo "  sudo tail -f /var/log/apache2/modsec_audit.log"
    echo ""
    echo "To adjust rate limits, edit the VirtualHost configuration file."
else
    echo "Configuration test failed! Please check the Apache error log:"
    echo "  sudo tail -f /var/log/apache2/error.log"
fi
