# Apache Rate Limiting Setup Script for Windows
# This script provides instructions for setting up rate limiting on Windows Apache

Write-Host "Apache Rate Limiting Setup for Windows" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""

Write-Host "IMPORTANT: This configuration is designed for Linux Apache servers." -ForegroundColor Yellow
Write-Host "For Windows Apache, you'll need to:" -ForegroundColor Yellow
Write-Host ""

Write-Host "1. Download and install required modules:" -ForegroundColor Cyan
Write-Host "   - mod_evasive: https://github.com/jzdziarski/mod_evasive"
Write-Host "   - mod_security: https://github.com/SpiderLabs/ModSecurity"
Write-Host "   - mod_limitipconn: https://dominia.org/djao/limitipconn2.html"
Write-Host ""

Write-Host "2. Enable modules in httpd.conf:" -ForegroundColor Cyan
Write-Host "   LoadModule evasive24_module modules/mod_evasive24.so"
Write-Host "   LoadModule security2_module modules/mod_security2.so"
Write-Host "   LoadModule limitipconn_module modules/mod_limitipconn.so"
Write-Host ""

Write-Host "3. Create required directories:" -ForegroundColor Cyan
Write-Host "   - C:\Apache24\logs\evasive"
Write-Host "   - C:\Apache24\conf\modsecurity"
Write-Host "   - C:\temp\modsecurity"
Write-Host ""

Write-Host "4. Copy bad-user-agents.txt to:" -ForegroundColor Cyan
Write-Host "   C:\Apache24\conf\modsecurity\bad-user-agents.txt"
Write-Host ""

Write-Host "5. Test configuration:" -ForegroundColor Cyan
Write-Host "   httpd -t"
Write-Host ""

Write-Host "6. Restart Apache service:" -ForegroundColor Cyan
Write-Host "   net stop Apache2.4"
Write-Host "   net start Apache2.4"
Write-Host ""

Write-Host "Alternative: Use IIS with Application Request Routing (ARR)" -ForegroundColor Magenta
Write-Host "============================================================" -ForegroundColor Magenta
Write-Host "For Windows servers, consider using IIS with ARR module which provides:"
Write-Host "- Built-in rate limiting"
Write-Host "- Dynamic IP restrictions"
Write-Host "- Request filtering"
Write-Host ""

# Check if running on Windows and provide Windows-specific guidance
if ($env:OS -eq "Windows_NT") {
    Write-Host "Detected Windows environment." -ForegroundColor Green
    Write-Host ""
    Write-Host "For production Windows deployments, consider:" -ForegroundColor Yellow
    Write-Host "1. IIS with Application Request Routing (ARR)"
    Write-Host "2. Cloudflare for DDoS protection"
    Write-Host "3. AWS Application Load Balancer with rate limiting"
    Write-Host "4. Azure Application Gateway with WAF"
    Write-Host ""
}

Write-Host "Configuration Summary:" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green
Write-Host "The apache-config.conf file now includes:"
Write-Host "- mod_evasive: 2 requests/page/second, 50 requests/site/second"
Write-Host "- mod_security: 100 requests/minute/IP, 20 POST requests/minute"
Write-Host "- mod_limitipconn: 10 concurrent connections/IP"
Write-Host "- Geographic blocking capabilities"
Write-Host "- Bad user agent blocking"
Write-Host "- Comprehensive logging"
Write-Host ""

Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
